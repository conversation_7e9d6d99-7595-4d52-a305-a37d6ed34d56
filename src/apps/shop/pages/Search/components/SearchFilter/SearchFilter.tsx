import { ReactNode, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  Popover,
  Button as ButtonMantine,
  UnstyledButton,
  Text,
  // Box,
  CloseButton,
} from '@mantine/core';
import { useTranslation } from 'react-i18next';
import SlidersIcon from '@/assets/images/product/sliders.svg?react';

import styles from './SearchFilter.module.css';
import { MultiSelect } from '@/libs/form/MultiSelect';
import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { Button } from '@/libs/ui/Button/Button';
import { VendorType } from '@/types';
import { Flex } from '@/libs/ui/Flex/Flex';
import { CONNECTION } from '@/apps/shop/constants';

interface FormValues {
  vendorIds: string[];
}

export interface SearchFilterProps {
  onChange: (filters: { vendorIds: string }) => void;
  initialValue: {
    vendorIds: string;
  };
  icon?: ReactNode;
  label?: ReactNode;
}

export const SearchFilter = ({
  onChange,
  initialValue,
  icon,
  label,
}: SearchFilterProps) => {
  const { getVendors } = useVendorsStore();
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [connectedVendors, setConnectedVendors] = useState<VendorType[]>([]);

  const { handleSubmit, setValue, getValues, reset, watch } =
    useForm<FormValues>();

  const vendorIds = watch('vendorIds', []);

  useEffect(() => {
    (async () => {
      await getVendors();

      const vendors = useVendorsStore
        .getState()
        .vendors.filter(
          (v) =>
            v.status === CONNECTION.CONNECTING ||
            v.status === CONNECTION.CONNECTED,
        );
      setConnectedVendors(vendors);

      if (initialValue.vendorIds) {
        const initialVendorIds = initialValue.vendorIds
          .split(',')
          .filter((vendorId) =>
            vendors.some((vendor) => vendor.id === vendorId),
          );
        setValue('vendorIds', initialVendorIds);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleToggle = () => {
    setIsOpen((prevState) => !prevState);
  };

  const handleSearch = handleSubmit((values) => {
    onChange({ vendorIds: values.vendorIds?.join(',') });
    setIsOpen(false);
  });

  return (
    <Popover position="bottom" shadow="md" opened={isOpen}>
      <Popover.Target>
        <ButtonMantine
          onClick={(event) => {
            event.stopPropagation();
            event.preventDefault();
            handleToggle();
          }}
          leftSection={icon ?? <SlidersIcon />}
          variant="default"
        >
          {label ? label : 'Filter by'}
          {getValues('vendorIds')?.length > 0 &&
            ` (${getValues('vendorIds').length})`}
        </ButtonMantine>
      </Popover.Target>

      <Popover.Dropdown className={styles.box}>
        <form onSubmit={handleSearch}>
          <Flex flex="row" justify="space-between" align="flex-start" mb="md">
            <Text size="1.25rem" fw="500" c="black">
              {t('client.search.filter')}
            </Text>
            <CloseButton type="submit" />
          </Flex>

          <div className={styles.fields}>
            <div className="rounded border border-[rgba(0,0,0,0.04)] bg-[rgba(242,244,247,1)] p-4">
              <MultiSelect
                label="Vendors"
                value={vendorIds}
                name="vendorIds"
                onChange={(e) =>
                  setValue('vendorIds', e.target.value as unknown as string[])
                }
                placeholder="Select vendors"
                options={connectedVendors.map((vendor) => ({
                  label: vendor.name,
                  value: vendor.id,
                }))}
              />
            </div>

            <div className="mt-4">
              <Button variant="secondary">
                {t('client.search.applyFilter')}
              </Button>
              <UnstyledButton
                className={styles.resetButton}
                type="button"
                onClick={() => reset()}
                mt="xs"
              >
                <Text ta="center" c="#0072C6" fw="500" mt="xs">
                  {t('client.search.resetFilter')}
                </Text>
              </UnstyledButton>
            </div>
          </div>
        </form>
      </Popover.Dropdown>
    </Popover>
  );
};
